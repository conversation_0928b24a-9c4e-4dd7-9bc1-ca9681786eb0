import { EyeOpenIcon } from "@radix-ui/react-icons";
import { Button, Dialog } from "@radix-ui/themes";
import Image from "next/image";
import Link from "next/link";
import { createContext, PropsWithChildren, use, useState } from "react";

type MediaData = {
  title: string;
  url: string;
  type: "image" | "pdf";
};

const MediaDialogContext = createContext<{
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
  data: MediaData;
  setData: (data: MediaData) => void;
}>({
  isVisible: false,
  setIsVisible: () => {},
  data: {
    title: "",
    url: "",
    type: "image",
  },
  setData: () => {},
});

export const MediaButton = ({
  title,
  url,
  type,
}: {
  title: string;
  url: string;
  type: "image" | "pdf";
}) => {
  const { setData, setIsVisible } = use(MediaDialogContext);

  const handleOpen = () => {
    setIsVisible(true);
    setData({ title, url, type });
  };

  return (
    <Button
      size="1"
      color="gray"
      radius="full"
      variant="soft"
      highContrast
      onClick={handleOpen}
    >
      Image <EyeOpenIcon />
    </Button>
  );
};

export const MediaDialogProvider = ({ children }: PropsWithChildren) => {
  const [isVisible, setIsVisible] = useState(false);
  const [data, setData] = useState<MediaData>({
    title: "",
    url: "",
    type: "image",
  });

  return (
    <MediaDialogContext
      value={{
        isVisible,
        setData,
        data,
        setIsVisible,
      }}
    >
      {children}
      <MediaDialog />
    </MediaDialogContext>
  );
};

const MediaDialog = () => {
  const { data, isVisible, setIsVisible } = use(MediaDialogContext);

  const { title, url, type } = data;

  return (
    <Dialog.Root open={isVisible} onOpenChange={setIsVisible}>
      <Dialog.Content>
        <Dialog.Title>{title}</Dialog.Title>
        {type === "image" && (
          <div className="flex justify-center">
            <Link href={url} target="_blank">
              <Image src={url} width={300} height={300} alt={title} />
            </Link>
          </div>
        )}
      </Dialog.Content>
    </Dialog.Root>
  );
};
