import { Table } from "@radix-ui/themes";
import { KycVerificationSearchResponse } from "@/api/data-contracts";
import { InfoLayout } from "@repo/ui/info-layout";
import { getCommonPinningStyles } from "@repo/ui/utils/table-utils";
import { LoadingPlaceholder } from "@repo/ui/loading-placeholder";

import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

import style from "./index.module.scss";
import { useKycColumns } from "./use-kyc-columns";
import { MediaDialogProvider } from "./media-dialog";
import type { KycSubmission } from "./kyc-review/context";

interface KycTableProps {
  data: KycVerificationSearchResponse[];
  isLoading?: boolean;
  onKycReview?: (value: KycSubmission) => void;
}

export const KycTable = ({
  data,
  isLoading = false,
  onKycReview,
}: KycTableProps) => {
  const columns = useKycColumns({ onKycReview });

  const table = useReactTable({
    data,
    columns,
    state: { columnPinning: { right: ["action"] } },
    getCoreRowModel: getCoreRowModel(),
  });

  if (!data.length) {
    if (isLoading) {
      return <LoadingPlaceholder />;
    }
    return (
      <div className="bg-[#00000008] rounded-lg">
        <InfoLayout
          className="py-10 px-6"
          icon="/empty-file.png"
          iconAlt="no data"
          title="No records found"
          description="KYC verification records will appear here"
        />
      </div>
    );
  }

  return (
    <MediaDialogProvider>
      <div className={style.tableContainer}>
        <Table.Root
          variant="surface"
          size="3"
          layout="fixed"
          className={style.override}
        >
          <Table.Header>
            {table.getHeaderGroups().map((headerGroup) => (
              <Table.Row key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <Table.ColumnHeaderCell
                    key={header.id}
                    style={{
                      ...getCommonPinningStyles(header.column),
                      minWidth: header.getSize(),
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </Table.ColumnHeaderCell>
                ))}
              </Table.Row>
            ))}
          </Table.Header>
          <Table.Body>
            {table.getRowModel().rows.map((row) => (
              <Table.Row key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Table.Cell
                    key={cell.id}
                    style={{
                      ...getCommonPinningStyles(cell.column),
                    }}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Table.Cell>
                ))}
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </div>
    </MediaDialogProvider>
  );
};
