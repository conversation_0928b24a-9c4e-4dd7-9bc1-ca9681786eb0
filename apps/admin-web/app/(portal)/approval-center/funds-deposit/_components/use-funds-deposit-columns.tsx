import { useMemo } from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { FiatDepositDetailsResponse } from "@/api/data-contracts";
import { CopyButton } from "@/ui-components/copy-button";
import { DepositStatusBadge } from "./deposit-status-badge";
import { DotsHorizontalIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import { AppMenu, type AppMenuItemProps } from "@repo/ui/app-menu";
import type { DepositSubmission } from "./deposit-review/context";

// Extended type to include depositType
type DepositRecord = FiatDepositDetailsResponse & {
  depositType: "FIAT" | "CRYPTO";
};

const columnHelper = createColumnHelper<DepositRecord>();

interface UseFundsDepositColumnsProps {
  onDepositReview?: (value: DepositSubmission) => void;
}

export const useFundsDepositColumns = ({ onDepositReview }: UseFundsDepositColumnsProps = {}) => {
  return useMemo(
    () => [
      columnHelper.accessor("userPublicId", {
        size: 240,
        header: "Portfolio ID",
        cell: ({ getValue }) => {
          const value = getValue();
          return (
            <div className="flex items-center gap-2">
              <span className="font-mono">{value}</span>
              {value && <CopyButton text={value} />}
            </div>
          );
        },
      }),
      columnHelper.accessor("transactionId", {
        size: 300,
        header: "Transaction ID",
        cell: ({ getValue }) => {
          const value = getValue();
          return (
            <div className="flex items-center gap-2">
              <span className="font-mono text-sm">{value}</span>
              {value && <CopyButton text={value} />}
            </div>
          );
        },
      }),
      columnHelper.accessor("status", {
        size: 140,
        header: "Status",
        cell: ({ getValue }) => {
          const status = getValue();
          return <DepositStatusBadge status={status} />;
        },
      }),
      columnHelper.accessor("depositType", {
        size: 180,
        header: "Deposit method",
        cell: ({ getValue }) => {
          const depositType = getValue();
          const isBlockchain = depositType === "CRYPTO";
          return (
            <span>
              {isBlockchain ? "Blockchain transfer" : "Bank transfer"}
            </span>
          );
        },
      }),
      columnHelper.accessor("amount", {
        size: 200,
        header: "Deposit amount",
        cell: ({ getValue, row }) => {
          const amount = getValue();
          const currency = row.original.currency;
          if (!amount) return "-";
          return (
            <span>
              {amount.toLocaleString()} {currency}
            </span>
          );
        },
      }),
      columnHelper.accessor("createdAt", {
        size: 200,
        header: "Deposit date",
        cell: ({ getValue }) => {
          const value = getValue();
          if (!value) return "-";
          const date = new Date(value);
          return (
            <span>
              {date.toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "2-digit",
                year: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
                hour12: false,
              })}
            </span>
          );
        },
      }),
      columnHelper.accessor("operatorAuditDetails", {
        size: 300,
        header: "Rejection reasons",
        cell: ({ row }) => {
          const status = row.original.status;
          const operatorAuditDetails = row.original.operatorAuditDetails;

          if (status === "REJECTED") {
            const reasonDescription = operatorAuditDetails?.reasonDescription;
            return <span>{reasonDescription || "Below minimum funds"}</span>;
          }
          return "-";
        },
      }),
      columnHelper.display({
        size: 80,
        id: "action",
        header: "Action",
        cell: ({ row }) => {
          const depositId = row.original.depositId;
          const status = row.original.status;

          const menuConfig: AppMenuItemProps[] = [];

          if (status === "PENDING" && depositId && onDepositReview) {
            menuConfig.push(
              {
                key: "APPROVE",
                label: "Approve",
                onClick: () => {
                  onDepositReview({ depositId, action: "APPROVE" });
                },
              },
              {
                key: "REJECT",
                label: "Reject",
                color: "red",
                onClick: () => {
                  onDepositReview({ depositId, action: "REJECT" });
                },
              },
            );
          }

          if (menuConfig.length === 0) {
            return (
              <div className="flex justify-center">
                <Button variant="ghost" m="auto" color="gray" size="1" disabled>
                  <DotsHorizontalIcon />
                </Button>
              </div>
            );
          }

          return (
            <div className="flex justify-center">
              <AppMenu config={menuConfig}>
                <Button variant="ghost" m="auto" color="gray" size="1">
                  <DotsHorizontalIcon />
                </Button>
              </AppMenu>
            </div>
          );
        },
      }),
    ],
    [onDepositReview],
  );
};
