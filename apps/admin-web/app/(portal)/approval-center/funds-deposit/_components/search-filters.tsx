import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Button, Select, TextField } from "@radix-ui/themes";
import { useState } from "react";

interface SearchFiltersProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  statusFilter: string;
  onStatusChange: (value: string) => void;
  depositTypeFilter: string;
  onDepositTypeChange: (value: string) => void;
  onSearch: () => void;
  onReset: () => void;
}

export const SearchFilters = ({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusChange,
  depositTypeFilter,
  onDepositTypeChange,
  onSearch,
  onReset,
}: SearchFiltersProps) => {
  const [value, setValue] = useState(searchQuery);

  const statusOptions = [
    { value: "PENDING", label: "Pending" },
    { value: "APPROVED", label: "Approved" },
    { value: "REJECTED", label: "Rejected" },
  ];

  const depositTypeOptions = [
    { value: "FIAT", label: "Fiat" },
    { value: "CRYPTO", label: "Crypto" },
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange(value);
    onSearch();
  };

  const handleReset = () => {
    setValue("");
    onSearchChange("");
    onStatusChange("");
    onDepositTypeChange("");
    onReset();
  };

  return (
    <form className="flex gap-4 items-end" onSubmit={handleSubmit}>
      <div className="flex-1 max-w-[400px]">
        <TextField.Root
          placeholder="Search for portfolio ID, email address, transaction ID"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          size="2"
        >
          <TextField.Slot side="left">
            <MagnifyingGlassIcon height="16" width="16" />
          </TextField.Slot>
        </TextField.Root>
      </div>

      <div className="min-w-[120px]">
        <Select.Root value={statusFilter} onValueChange={onStatusChange}>
          <Select.Trigger placeholder="Status" />
          <Select.Content>
            {statusOptions.map(({ value, label }) => (
              <Select.Item key={value} value={value}>
                <div className="min-w-[80px]">{label}</div>
              </Select.Item>
            ))}
          </Select.Content>
        </Select.Root>
      </div>

      <div className="min-w-[120px]">
        <Select.Root
          value={depositTypeFilter}
          onValueChange={onDepositTypeChange}
        >
          <Select.Trigger placeholder="Deposit type" />
          <Select.Content>
            {depositTypeOptions.map(({ value, label }) => (
              <Select.Item key={value} value={value}>
                <div className="min-w-[80px]">{label}</div>
              </Select.Item>
            ))}
          </Select.Content>
        </Select.Root>
      </div>

      <Button
        type="submit"
        radius="full"
        color="gray"
        highContrast
        variant="soft"
        size="2"
      >
        Search
      </Button>

      <Button
        type="button"
        radius="full"
        color="gray"
        variant="outline"
        size="2"
        onClick={handleReset}
      >
        Reset
      </Button>
    </form>
  );
};
