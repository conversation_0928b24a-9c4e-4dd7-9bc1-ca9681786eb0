import { use, useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Dialog, Button, Flex, Text, Select, TextArea } from "@radix-ui/themes";
import service from "@/api";
import { DepositReviewDialogContext } from "./context";
import type { DepositReviewRequest } from "@/api/data-contracts";

export const DepositReviewDialog = () => {
  const { submission, setSubmission } = use(DepositReviewDialogContext);
  const [reason, setReason] = useState("");
  const [reasonExtra, setReasonExtra] = useState("");
  const queryClient = useQueryClient();

  const reviewMutation = useMutation({
    mutationFn: async (data: DepositReviewRequest) => {
      return await service.reviewDeposit(data);
    },
    onSuccess: () => {
      // Invalidate and refetch deposit queries
      queryClient.invalidateQueries({ queryKey: ["fiat-deposits"] });
      queryClient.invalidateQueries({ queryKey: ["crypto-deposits"] });
      setSubmission(null);
      setReason("");
      setReasonExtra("");
    },
  });

  const handleSubmit = () => {
    if (!submission) return;

    reviewMutation.mutate({
      depositId: submission.depositId,
      action: submission.action,
      reason: reason as DepositReviewRequest["reason"],
      reasonExtra: reasonExtra || undefined,
    });
  };

  const handleClose = () => {
    setSubmission(null);
    setReason("");
    setReasonExtra("");
  };

  const reasonOptions = [
    { value: "APPROVED", label: "Approved" },
    { value: "INVALID_DOCUMENT", label: "Invalid Document" },
    { value: "DOCUMENT_UNREADABLE", label: "Document Unreadable" },
    { value: "BANK_ACCOUNT_MISMATCH", label: "Bank Account Mismatch" },
    { value: "INVALID_BANK_DETAILS", label: "Invalid Bank Details" },
    {
      value: "INSUFFICIENT_PROOF_OF_TRANSFER",
      label: "Insufficient Proof of Transfer",
    },
    { value: "INVALID_TRANSACTION_HASH", label: "Invalid Transaction Hash" },
    { value: "INCORRECT_CRYPTO_CURRENCY", label: "Incorrect Crypto Currency" },
    { value: "WALLET_ADDRESS_MISMATCH", label: "Wallet Address Mismatch" },
    { value: "AMOUNT_MISMATCH", label: "Amount Mismatch" },
    { value: "AMOUNT_BELOW_MINIMUM", label: "Amount Below Minimum" },
    {
      value: "INVALID_TRANSACTION_AMOUNT",
      label: "Invalid Transaction Amount",
    },
    { value: "SUSPICIOUS_AMOUNT_PATTERN", label: "Suspicious Amount Pattern" },
    { value: "COMPLIANCE_VIOLATION", label: "Compliance Violation" },
    { value: "SANCTIONS_LIST_MATCH", label: "Sanctions List Match" },
    { value: "SUSPICIOUS_SOURCE", label: "Suspicious Source" },
    { value: "OTHER_REASON", label: "Other Reason" },
  ];

  return (
    <Dialog.Root open={!!submission} onOpenChange={handleClose}>
      <Dialog.Content maxWidth="500px">
        <Dialog.Title>
          {submission?.action === "APPROVE" ? "Approve" : "Reject"} Deposit
        </Dialog.Title>
        <Dialog.Description size="2" mb="4">
          Please provide a reason for your decision.
        </Dialog.Description>

        <Flex direction="column" gap="3">
          <div>
            <Text as="label" size="2" weight="bold">
              Reason
            </Text>
            <Select.Root value={reason} onValueChange={setReason}>
              <Select.Trigger placeholder="Select a reason" />
              <Select.Content>
                {reasonOptions.map(({ value, label }) => (
                  <Select.Item key={value} value={value}>
                    {label}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Root>
          </div>

          <div>
            <Text as="label" size="2" weight="bold">
              Additional Notes (Optional)
            </Text>
            <TextArea
              placeholder="Enter additional notes..."
              value={reasonExtra}
              onChange={(e) => setReasonExtra(e.target.value)}
              maxLength={500}
            />
          </div>
        </Flex>

        <Flex gap="3" mt="4" justify="end">
          <Dialog.Close>
            <Button variant="soft" color="gray">
              Cancel
            </Button>
          </Dialog.Close>
          <Button
            onClick={handleSubmit}
            loading={reviewMutation.isPending}
            color={submission?.action === "APPROVE" ? "green" : "red"}
          >
            {submission?.action === "APPROVE" ? "Approve" : "Reject"}
          </Button>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};
