"use client";
import { use, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, Head<PERSON> } from "@radix-ui/themes";
import service from "@/api";
import {
  FiatDepositDetailsResponse,
  PagedFiatDepositDetailsResponse,
} from "@/api/data-contracts";
import { SearchFilters } from "./_components/search-filters";
import { FundsDepositTable } from "./_components/funds-deposit-table";
import { Pagination } from "@repo/ui/pagination";
import { DownloadIcon } from "@radix-ui/react-icons";
import { InfoLayout } from "@repo/ui/info-layout";
import { DepositReviewDialogContext } from "./_components/deposit-review/context";
import { DepositReviewDialogProvider } from "./_components/deposit-review";

const Page = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [depositTypeFilter, setDepositTypeFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const { setSubmission } = use(DepositReviewDialogContext);

  // Fetch both fiat and crypto deposits
  const {
    data: fiatData,
    isLoading: fiatLoading,
    error: fiatError,
  } = useQuery({
    queryKey: [
      "fiat-deposits",
      searchQuery,
      statusFilter,
      currentPage,
      pageSize,
      depositTypeFilter,
    ],
    queryFn: async () => {
      if (depositTypeFilter && depositTypeFilter !== "FIAT") {
        return {
          records: [],
          totalPages: 0,
        } satisfies PagedFiatDepositDetailsResponse;
      }

      const searchParams: {
        userPublicId?: string;
        userEmail?: string;
        transactionId?: string;
        depositStatus?: "PENDING" | "APPROVED" | "REJECTED";
        page: number;
        pageSize: number;
      } = {
        page: currentPage,
        pageSize,
      };

      // Determine if search query is email, portfolio ID, or transaction ID
      if (searchQuery.trim()) {
        if (searchQuery.includes("@")) {
          searchParams.userEmail = searchQuery.trim();
        } else if (searchQuery.includes("-")) {
          // Assume it's a transaction ID if it contains hyphens
          searchParams.transactionId = searchQuery.trim();
        } else {
          searchParams.userPublicId = searchQuery.trim();
        }
      }

      // Add status filter if selected
      if (statusFilter) {
        searchParams.depositStatus = statusFilter as
          | "PENDING"
          | "APPROVED"
          | "REJECTED";
      }

      const response = await service.getFiatDepositsInfo(searchParams);
      return response.data?.data;
    },
  });

  const {
    data: cryptoData,
    isLoading: cryptoLoading,
    error: cryptoError,
  } = useQuery({
    queryKey: [
      "crypto-deposits",
      searchQuery,
      statusFilter,
      currentPage,
      pageSize,
      depositTypeFilter,
    ],
    queryFn: async () => {
      if (depositTypeFilter && depositTypeFilter !== "CRYPTO") {
        return {
          records: [],
          totalPages: 0,
        } satisfies PagedFiatDepositDetailsResponse;
      }

      const searchParams: {
        userPublicId?: string;
        userEmail?: string;
        transactionId?: string;
        depositStatus?: "PENDING" | "APPROVED" | "REJECTED";
        page: number;
        pageSize: number;
      } = {
        page: currentPage,
        pageSize,
      };

      // Determine if search query is email, portfolio ID, or transaction ID
      if (searchQuery.trim()) {
        if (searchQuery.includes("@")) {
          searchParams.userEmail = searchQuery.trim();
        } else if (searchQuery.includes("-")) {
          // Assume it's a transaction ID if it contains hyphens
          searchParams.transactionId = searchQuery.trim();
        } else {
          searchParams.userPublicId = searchQuery.trim();
        }
      }

      // Add status filter if selected
      if (statusFilter) {
        searchParams.depositStatus = statusFilter as
          | "PENDING"
          | "APPROVED"
          | "REJECTED";
      }

      const response = await service.getCryptoDepositsInfo(searchParams);
      return response.data?.data;
    },
  });

  // Combine and process data
  const fiatRecords = fiatData?.records || [];
  const cryptoRecords = cryptoData?.records || [];

  // Add depositType to records for display
  const allRecords = [
    ...fiatRecords.map((record: FiatDepositDetailsResponse) => ({
      ...record,
      depositType: "FIAT" as const,
    })),
    ...cryptoRecords.map((record: FiatDepositDetailsResponse) => ({
      ...record,
      depositType: "CRYPTO" as const,
    })),
  ];

  const isLoading = fiatLoading || cryptoLoading;
  const error = fiatError || cryptoError;
  const totalPages = Math.max(
    fiatData?.totalPages || 0,
    cryptoData?.totalPages || 0,
  );

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleReset = () => {
    setCurrentPage(1); // Reset to first page when resetting
  };

  const handleExportData = () => {
    // TODO: Implement export functionality
    console.log("Export data functionality to be implemented");
  };

  return (
    <main className="grow">
      <div className="flex justify-between items-center mb-6">
        <Heading size="7" weight="medium">
          Funds deposit
        </Heading>
        <Button
          radius="full"
          color="gray"
          highContrast
          variant="soft"
          size="2"
          onClick={handleExportData}
        >
          <DownloadIcon />
          Export data
        </Button>
      </div>

      <div className="mb-6">
        <SearchFilters
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          statusFilter={statusFilter}
          onStatusChange={setStatusFilter}
          depositTypeFilter={depositTypeFilter}
          onDepositTypeChange={setDepositTypeFilter}
          onSearch={handleSearch}
          onReset={handleReset}
        />
      </div>

      {error ? (
        <div className="bg-[#00000008] rounded-lg">
          <InfoLayout
            className="py-10 px-6"
            icon="/empty-file.png"
            iconAlt="no data"
            title="Error"
            description="An error occurred while fetching deposit information"
          />
        </div>
      ) : (
        <>
          <FundsDepositTable
            data={allRecords}
            isLoading={isLoading}
            onDepositReview={setSubmission}
          />
          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              className="justify-end mt-6"
              nextText="Next"
              previousText="Previous"
            />
          )}
        </>
      )}
    </main>
  );
};

export default function FundsDepositPage() {
  return (
    <DepositReviewDialogProvider>
      <Page />
    </DepositReviewDialogProvider>
  );
}
