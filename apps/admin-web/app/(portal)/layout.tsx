import { PropsWithChildren } from "react";
import { AdminNav } from "../components/admin-nav";
import { UserMenu } from "../components/user-menu";
import { ToastProvider } from "@repo/ui/toast-provider/index";
import { Toast } from "./toast";

const config = [
  {
    href: "/dashboard",
    label: "Profile Overview",
  },
  {
    href: "/user-management",
    label: "User Management",
  },
  {
    href: "/approval-center",
    label: "Approval Center",
    children: [
      {
        href: "/approval-center/kyc-verification",
        label: "KYC verification",
      },
      {
        href: "/approval-center/funds-deposit",
        label: "Funds deposit",
      },
    ],
  },
];

const Layout = ({ children }: PropsWithChildren) => {
  return (
    <ToastProvider instance={Toast}>
      <AdminNav config={config}>
        <UserMenu />
      </AdminNav>
      {children}
    </ToastProvider>
  );
};

export default Layout;
