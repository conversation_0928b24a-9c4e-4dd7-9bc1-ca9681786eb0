import { PropsWithChildren } from "react";
import { useTranslations } from "next-intl";

import { ToastProvider } from "@repo/ui/toast-provider/index";
import { Container } from "@/ui-components/layout/container";
import { AppNav } from "@/ui-components/nav-link";
import { UserAvatar } from "@/ui-components/user-avatar";
import "./layout.scss";
import { Toast } from "./toast";

const DashboardLayout = ({ children }: PropsWithChildren) => {
  const t = useTranslations("Portal.Nav");

  const dashboardLinks = [
    {
      href: "/dashboard",
      label: t("dashboard"),
    },
    {
      href: "/transaction-history",
      label: t("transactionHistory"),
    },
  ];

  return (
    <ToastProvider instance={Toast}>
      <div className="min-h-dvh flex flex-col">
        <nav className="sticky top-0 bg-white z-10 border-b border-gray-200">
          <Container>
            <div className="section-content">
              <AppNav
                linkConfig={dashboardLinks}
                action={<UserAvatar linkConfig={dashboardLinks} />}
              />
            </div>
          </Container>
        </nav>
        {children}
      </div>
    </ToastProvider>
  );
};

export default DashboardLayout;
