.textOrange {
  color: #b65605;
}

.textBlue {
  color: #1178ca;
}

.textGray {
  color: #00000080;
}

.smWhite {
  @media (max-width: calc(48rem - 1px)) {
    color: #ffffff;
  }
}

.heading1 {
  font-weight: 700;
  font-size: 56px;
  line-height: 64px;

  @media (min-width: 48rem) {
    font-size: 64px;
    line-height: 68px;
  }
}

.heading2Lg {
  font-weight: 700;
  font-size: 40px;
  line-height: 44px;
  @media (min-width: 48rem) {
    font-size: 48px;
    line-height: 52px;
  }
}

.heading2 {
  font-weight: 700;
  font-size: 32px;
  line-height: 36px;

  @media (min-width: 48rem) {
    font-size: 36px;
    line-height: 42px;
  }
}

.heading3 {
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;

  @media (min-width: 48rem) {
    font-size: 18px;
    line-height: 22px;
  }
}

.caption {
  font-size: 12px;
  line-height: 16px;

  @media (min-width: 48rem) {
    font-size: 16px;
    line-height: 24px;
  }
}

.paragraph {
  font-size: 16px;
  line-height: 20px;

  @media (min-width: 48rem) {
    font-size: 18px;
    line-height: 22px;
  }
}

.paragraph2 {
  font-size: 14px;
  line-height: 18px;

  @media (min-width: 48rem) {
    font-size: 16px;
    line-height: 20px;
  }
}

.smallLinks {
  display: flex;
  gap: 32px;

  > div {
    position: relative;
  }

  > div:not(:last-child):after {
    content: "";
    position: absolute;
    top: calc(50% - 0.4em);
    left: calc(100% + 15px);
    width: 1px;
    height: 0.8em;
    background: #00000020;
  }
}
