"use client";
import { Container } from "@/ui-components/layout/container";
import { LandingPageFooter } from "./_components/landing-page-footer";
import { AppNav } from "@/ui-components/nav-link";
import { useTranslations } from "next-intl";
import { PropsWithChildren } from "react";
import { LandingPageMenu } from "./_components/landing-page-nav-actions";
import { renderLink } from "@/ui-components/nav-link/utils";

const LandingPageLayout = ({ children }: PropsWithChildren) => {
  const t = useTranslations();
  const linkConfig = [
    {
      href: "/about",
      label: t("Nav.about"),
    },

    {
      href: "/invest",
      label: t("Nav.invest"),
    },
  ];

  return (
    <div className="min-h-dvh flex flex-col">
      <nav className="sticky top-0 bg-white z-10 border-b border-gray-200">
        <Container>
          <div className="section-content">
            <AppNav
              linkConfig={linkConfig}
              action={
                <LandingPageMenu linkConfig={linkConfig.map(renderLink)} />
              }
            />
          </div>
        </Container>
      </nav>
      {children}
      <footer>
        <Container>
          <div className="section-content">
            <LandingPageFooter config={linkConfig} />
          </div>
        </Container>
      </footer>
    </div>
  );
};

export default LandingPageLayout;
