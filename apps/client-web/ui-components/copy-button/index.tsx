"use client";
import { CopyIcon } from "@radix-ui/react-icons";
import { Toast } from "@/app/[locale]/(portal)/toast";

interface CopyButtonProps {
  text: string;
  className?: string;
}

export const CopyButton = ({ text, className }: CopyButtonProps) => {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      Toast.show("Copied", "success");
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  return (
    <CopyIcon
      className={`w-4 h-4 shrink-0 text-gray-500 cursor-pointer hover:text-gray-700 ${className}`}
      onClick={handleCopy}
    />
  );
};
